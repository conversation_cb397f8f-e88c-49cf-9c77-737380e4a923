from datetime import date
from typing import List, Optional, Union
from fastapi import FastAPI, Form
from pydantic import BaseModel, Field, validator
import uvicorn

class Addr(BaseModel):
    province: str
    city: str

class User(BaseModel):
    name: str = Field(default='root')
    age: int = Field(default=0, lt=100, gt=0, description="年龄")
    birth: Optional[date] = None
    friends: List[int] = []
    addr: Union[Addr, None] = None
    description: Union[str, None] = None

    @validator('name')
    def name_must_alpha(cls, v):
        assert v.isalpha(), 'name must be alpha'
        return v

class Data(BaseModel):
    users: List[User]

app = FastAPI()

@app.post("/user")
async def create_user(data: Data):
    return data

@app.post("/regin")
async def regin(username: str = Form(..., max_length=16, min_length=8, regex='[a-zA-Z]'),
                password: str = Form(..., min_length=8, max_length=16, regex='[0-9]')):
    print(f"username:{username},password:{password}")
    return {"username": username, "password": password}

if __name__ == '__main__':
    try:
        User(name="", age=100)
    except Exception as e:
        print(e.json())
    uvicorn.run("main:app", host="127.0.0.1", port=8080, reload=True)
    