from fastapi import FastAPI, Request
from fastapi.templating import <PERSON><PERSON>2Templates
import uvicorn

app = FastAPI()

templates = Jinja2Templates(directory="templates")

@app.get("/")
async def index(request: Request):
    return templates.TemplateResponse("index.html",
                                       {
                                           "request": request,
                                           "message": "Hello World",
                                           "user": 'yuan',
                                           "books": ['python', 'java', 'c++'],
                                           "booksDict": {
                                               "python": {"price": 100, "publish": "人民1出版社"},
                                               "java": {"price": 200, "publish": "人民2出版社"},
                                               "c++": {"price": 300, "publish": "人民3出版社"}
                                           }
                                    
                                       })

if __name__ == '__main__':
    
    uvicorn.run("main:app", host="127.0.0.1", port=8080, reload=True)