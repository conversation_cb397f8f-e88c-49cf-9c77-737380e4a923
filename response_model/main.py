from fastapi import FastAPI
import uvicorn
from pydantic import BaseModel, EmailStr
from typing import Union

class UserIn(BaseModel):
    username: str
    password: str
    email: EmailStr
    full_name: Union[str, None] = None

class UserOut(BaseModel):
    username: str
    email: EmailStr
    full_name: Union[str, None] = None

app = FastAPI()

@app.post("/user/", response_model=UserOut)
async def create_user(user : UserIn):
    return user

if __name__ == '__main__':
    
    uvicorn.run("main:app", host="127.0.0.1", port=8080, reload=True)