from fastapi import FastAPI
from typing import Union
import uvicorn
app = FastAPI()

@app.get("/get",tags=["get"],summary="get method",description="get method")
async def get_test():
    return {"method": "get"}

@app.post("/post")
async def post_test():
    return {"method": "post"}

@app.put("/put")
async def put_test():
    return {"method": "put"}

@app.delete("/delete")
async def delete_test():
    return {"method": "delete"}

@app.options("/options")
async def options_test():
    return {"method": "options"}

@app.head("/head")
async def head_test():
    return {"method": "head"}

@app.patch("/patch")
async def patch_test():
    return {"method": "patch"}

@app.trace("/trace")
async def trace_test():
    return {"method": "trace"}


if __name__ == '__main__':
    
    uvicorn.run("requestMethod:app", host="127.0.0.1", port=8080, reload=True)
