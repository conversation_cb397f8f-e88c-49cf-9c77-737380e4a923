from fastapi import FastAPI
app = FastAPI()
import uvicorn
from typing import Union


@app.get("/user/{user_id}")
async def get_user(user_id: int):
    print(user_id, type)
    return {"user_id": user_id}

@app.get("/jobs/{kd}")
async def search_jobs(kd: str, city: Union[str, None] = None, xl: Union[str, None] = None ):
    if city or xl:
        return {"kd": kd, "city": city, "xl": xl}
    return {"kd": kd}


if __name__ == '__main__':
    
    uvicorn.run("main:app", host="127.0.0.1", port=8080, reload=True)