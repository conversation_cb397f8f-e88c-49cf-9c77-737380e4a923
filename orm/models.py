from tortoise.models import Model
from tortoise import fields

class Clas(Model):
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=20, description="班级名称")

class Student(Model):
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=20, description="姓名")
    sno = fields.IntField(description="学号")
    # 一对多 定义反向引用名称，通过班级对象可以用 clas.students 获取该班级的所有学生
    clas = fields.ForeignKeyField("models.Clas", related_name="students", description="班级")
    # 多对多 反向引用名称，通过课程对象可以用 course.students 获取选了该课程的所有学生
    courses = fields.ManyToManyField("models.Course", related_name="students",description="学生选课表")

class Teacher(Model):
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=20, description="姓名")
    pwd = fields.CharField(max_length=20, description="密码")
    tno = fields.IntField(description="账号")

class Course(Model):
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=20, description="课程名")
    teacher = fields.ForeignKeyField("models.Teacher", related_name="courses", description="课程讲师")
    pass
    