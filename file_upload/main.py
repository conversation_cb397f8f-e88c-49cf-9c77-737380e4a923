from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, Request
from fastapi.staticfiles import StaticFiles
from typing import List
import uvicorn

app = FastAPI()

app.mount("/static", StaticFiles(directory="statics"))

@app.post("/files/")
async def create_file(file: bytes = File()):
  print("file:", file)
  return {"file_size": len(file)}

@app.post("/multiFiles/")
async def create_files(files: List[bytes] = File()):
  return {"file_size": [len(file) for file in files] }

@app.post("/uploadFile/")
async def create_upload_file(file: UploadFile):
  with open (f"{file.filename}", "wb") as f:
    for chunk in iter(lambda: file.file.read(1024), b''):
      f.write(chunk)
  return {"filename": file.filename}

@app.post("/multiUploadFiles/")
async def create_upload_files(files: List[UploadFile]):
  return {"filename": [file.filename for file in files]}

@app.get("/items")
async def items(request: Request):
  return {
    "请求 URL": request.url,
    "请求方法": request.method,
    "请求ip": request.client.host,
    "请求头": request.headers,
    "请求体": request.query_params,
    "cookies": request.cookies
  }

if __name__ == '__main__':
    
    uvicorn.run("main:app", host="127.0.0.1", port=8080, reload=True)